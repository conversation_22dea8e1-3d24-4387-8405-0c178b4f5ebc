<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        // For now, return a simple view
        // In the future, this would integrate with a proper gallery system
        return view('owner.gallery.index', compact('business'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('owner.gallery.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Gallery upload functionality would be implemented here
        return redirect()->route('owner.gallery.index')
            ->with('success', 'Image uploaded successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Show individual gallery item
        return view('owner.gallery.show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return view('owner.gallery.edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return redirect()->route('owner.gallery.index')
            ->with('success', 'Image updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('owner.gallery.index')
            ->with('success', 'Image deleted successfully!');
    }

    /**
     * Get user's business.
     */
    private function getUserBusiness()
    {
        return Business::where('owner_id', Auth::id())->first();
    }
}
