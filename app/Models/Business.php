<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class Business extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'address',
        'email',
        'phone',
        'website',
        'logo',
        'branding',
        'timezone',
        'currency',
        'language',
        'multi_branch',
        'online_booking_enabled',
        'landing_page_enabled',
        'landing_page_slug',
        'landing_page_status',
        'custom_domain',
        'domain_type',
        'ssl_enabled',
        'landing_page_theme',
        'landing_page_config',
        'seo_optimized',
        'landing_page_last_updated',
        'booking_advance_days',
        'booking_advance_hours',
        'cancellation_hours',
        'business_rules',
        'is_active',
        'owner_id',
    ];

    protected $casts = [
        'branding' => 'array',
        'business_rules' => 'array',
        'landing_page_config' => 'array',
        'multi_branch' => 'boolean',
        'online_booking_enabled' => 'boolean',
        'landing_page_enabled' => 'boolean',
        'ssl_enabled' => 'boolean',
        'seo_optimized' => 'boolean',
        'is_active' => 'boolean',
        'landing_page_last_updated' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($business) {
            if (empty($business->slug)) {
                $business->slug = Str::slug($business->name);
            }
        });
    }

    // Relationships
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function branches(): HasMany
    {
        return $this->hasMany(BusinessBranch::class);
    }

    public function mainBranch(): HasMany
    {
        return $this->hasMany(BusinessBranch::class)->where('is_main_branch', true);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(BusinessCategory::class, 'business_category_assignments', 'business_id', 'business_category_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(BusinessTag::class, 'business_tag_assignments');
    }

    public function operatingHours(): HasMany
    {
        return $this->hasMany(BusinessOperatingHour::class);
    }

    public function holidays(): HasMany
    {
        return $this->hasMany(BusinessHoliday::class);
    }

    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }

    public function serviceCategories(): HasMany
    {
        return $this->hasMany(ServiceCategory::class);
    }

    public function resources(): HasMany
    {
        return $this->hasMany(Resource::class);
    }

    public function resourceTypes(): HasMany
    {
        return $this->hasMany(ResourceType::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function availabilityBlocks(): HasMany
    {
        return $this->hasMany(AvailabilityBlock::class);
    }

    public function waitingLists(): HasMany
    {
        return $this->hasMany(WaitingList::class);
    }

    // Customer relationships
    public function customerProfiles(): HasMany
    {
        return $this->hasMany(CustomerBusinessProfile::class);
    }

    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'customer_business_profiles', 'business_id', 'customer_id')
                    ->withPivot([
                        'status', 'customer_since', 'last_visit_date', 'total_visits',
                        'total_spent', 'loyalty_points_balance', 'loyalty_tier'
                    ])
                    ->withTimestamps();
    }

    public function activeCustomers(): BelongsToMany
    {
        return $this->customers()->wherePivot('status', 'active');
    }

    public function vipCustomers(): BelongsToMany
    {
        return $this->customers()->wherePivot('status', 'vip');
    }

    public function customerTags(): HasMany
    {
        return $this->hasMany(CustomerTag::class);
    }

    public function customerCommunications(): HasMany
    {
        return $this->hasMany(CustomerCommunication::class);
    }

    public function customerLoyaltyPoints(): HasMany
    {
        return $this->hasMany(CustomerLoyaltyPoint::class);
    }

    // Landing Page relationships
    public function landingPage(): HasOne
    {
        return $this->hasOne(BusinessLandingPage::class);
    }

    public function seoSettings(): HasOne
    {
        return $this->hasOne(BusinessSeoSettings::class);
    }

    public function landingServiceSettings(): HasOne
    {
        return $this->hasOne(LandingServiceSettings::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getBrandingAttribute($value)
    {
        $default = [
            'primary_color' => '#007bff',
            'secondary_color' => '#6c757d',
            'theme' => 'default',
        ];

        return $value ? array_merge($default, json_decode($value, true)) : $default;
    }
}
