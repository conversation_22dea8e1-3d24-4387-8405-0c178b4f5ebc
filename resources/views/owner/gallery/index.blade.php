@extends('owner.layouts.app')

@section('title', 'Gallery Management')

@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Gallery Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Gallery</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Gallery Images</h3>
                <div class="card-tools">
                    <a href="{{ route('owner.gallery.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus mr-1"></i>
                        Upload Images
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>Gallery Management</strong><br>
                    This section will allow you to upload and manage images for your business gallery.
                    Images uploaded here can be displayed in your landing page gallery section.
                    <br><br>
                    <strong>Features coming soon:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Image upload with drag & drop</li>
                        <li>Image categorization and tagging</li>
                        <li>Bulk image management</li>
                        <li>Image optimization and resizing</li>
                        <li>Gallery organization tools</li>
                    </ul>
                </div>

                <div class="text-center py-5">
                    <i class="fas fa-images fa-4x text-muted mb-3"></i>
                    <h4>Gallery Management Coming Soon</h4>
                    <p class="text-muted">
                        We're working on a comprehensive gallery management system that will allow you to:
                    </p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-upload text-primary mb-2"></i>
                                        <h6>Easy Upload</h6>
                                        <small class="text-muted">Drag & drop multiple images at once</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-tags text-success mb-2"></i>
                                        <h6>Organization</h6>
                                        <small class="text-muted">Categorize and tag your images</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-compress-alt text-warning mb-2"></i>
                                        <h6>Optimization</h6>
                                        <small class="text-muted">Automatic image compression and resizing</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-eye text-info mb-2"></i>
                                        <h6>Preview</h6>
                                        <small class="text-muted">See how images appear on your landing page</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <p class="text-muted">
                            In the meantime, you can configure your gallery section in the
                            <a href="{{ route('owner.landing-page.edit') }}" class="text-primary">Landing Page Editor</a>.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.feature-item {
    text-align: center;
}
.feature-item i {
    font-size: 2rem;
    display: block;
}
</style>
@endpush
