@extends('owner.layouts.app')

@section('title', 'View Image')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Image Details</h1>
            <p class="text-muted mb-0">View and manage individual gallery image</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-image mr-2"></i>
                Image Details
            </h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Image Management Coming Soon</strong><br>
                Individual image viewing and management features are currently under development.
            </div>

            <div class="text-center py-5">
                <i class="fas fa-image fa-4x text-muted mb-3"></i>
                <h4>Image Details View</h4>
                <p class="text-muted">
                    This page will show detailed information about individual gallery images including:
                </p>
                
                <div class="row justify-content-center mt-4">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-info text-primary mb-2"></i>
                                    <h6>Image Information</h6>
                                    <small class="text-muted">Title, description, and metadata</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-chart-bar text-success mb-2"></i>
                                    <h6>Usage Statistics</h6>
                                    <small class="text-muted">Views and engagement metrics</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-edit text-warning mb-2"></i>
                                    <h6>Edit Options</h6>
                                    <small class="text-muted">Crop, resize, and apply filters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-share-alt text-info mb-2"></i>
                                    <h6>Sharing Options</h6>
                                    <small class="text-muted">Direct links and social sharing</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('styles')
<style>
.feature-item {
    text-align: center;
}
.feature-item i {
    font-size: 2rem;
    display: block;
}
</style>
@endpush
