@extends('owner.layouts.app')

@section('title', 'Edit Image')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Edit Image</h1>
            <p class="text-muted mb-0">Modify image details and settings</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-edit mr-2"></i>
                Edit Image
            </h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Image Editing Coming Soon</strong><br>
                Advanced image editing and management features are currently under development.
            </div>

            <div class="text-center py-5">
                <i class="fas fa-edit fa-4x text-muted mb-3"></i>
                <h4>Image Editor</h4>
                <p class="text-muted">
                    This page will provide comprehensive image editing capabilities including:
                </p>
                
                <div class="row justify-content-center mt-4">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-crop text-primary mb-2"></i>
                                    <h6>Crop & Resize</h6>
                                    <small class="text-muted">Adjust image dimensions and aspect ratio</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-palette text-success mb-2"></i>
                                    <h6>Color Adjustments</h6>
                                    <small class="text-muted">Brightness, contrast, and saturation</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-tags text-warning mb-2"></i>
                                    <h6>Metadata Editing</h6>
                                    <small class="text-muted">Title, description, and tags</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item mb-3">
                                    <i class="fas fa-filter text-info mb-2"></i>
                                    <h6>Filters & Effects</h6>
                                    <small class="text-muted">Apply professional filters</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Placeholder Edit Form (for future implementation) -->
                <div class="card bg-light mt-4" style="display: none;">
                    <div class="card-body">
                        <form action="#" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title">Image Title</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               placeholder="Enter image title">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category">Category</label>
                                        <select class="form-control" id="category" name="category">
                                            <option value="">Select a category</option>
                                            <option value="portfolio">Portfolio</option>
                                            <option value="before-after">Before & After</option>
                                            <option value="team">Team</option>
                                            <option value="facility">Facility</option>
                                            <option value="products">Products</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="Add a description for this image..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="alt_text">Alt Text (for accessibility)</label>
                                <input type="text" class="form-control" id="alt_text" name="alt_text" 
                                       placeholder="Describe the image for screen readers">
                            </div>

                            <div class="form-group">
                                <label for="tags">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags" 
                                       placeholder="Enter tags separated by commas">
                                <small class="form-text text-muted">
                                    Tags help organize and search your images
                                </small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_featured" name="is_featured">
                                        <label class="custom-control-label" for="is_featured">
                                            Featured Image
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_public" name="is_public" checked>
                                        <label class="custom-control-label" for="is_public">
                                            Public (visible on website)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-1"></i>
                                    Save Changes
                                </button>
                                <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('styles')
<style>
.feature-item {
    text-align: center;
}
.feature-item i {
    font-size: 2rem;
    display: block;
}
</style>
@endpush
