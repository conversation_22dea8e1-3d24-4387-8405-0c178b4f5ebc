@extends('owner.layouts.app')

@section('title', 'Upload Images')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Upload Images</h1>
            <p class="text-muted mb-0">Add new images to your business gallery</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-upload mr-2"></i>
                Image Upload
            </h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Gallery Upload Coming Soon</strong><br>
                We're working on a comprehensive image upload system that will include:
                <ul class="mb-0 mt-2">
                    <li>Drag & drop multiple image upload</li>
                    <li>Image preview and editing</li>
                    <li>Automatic image optimization</li>
                    <li>Category assignment and tagging</li>
                    <li>Bulk image management</li>
                </ul>
            </div>

            <div class="text-center py-5">
                <i class="fas fa-cloud-upload-alt fa-4x text-muted mb-3"></i>
                <h4>Image Upload System</h4>
                <p class="text-muted">
                    This feature is currently under development. You'll soon be able to upload and manage your business images here.
                </p>
                
                <div class="row justify-content-center mt-4">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-preview mb-3">
                                    <i class="fas fa-images text-primary mb-2"></i>
                                    <h6>Multiple Formats</h6>
                                    <small class="text-muted">Support for JPG, PNG, WebP, and more</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-preview mb-3">
                                    <i class="fas fa-compress-arrows-alt text-success mb-2"></i>
                                    <h6>Auto Optimization</h6>
                                    <small class="text-muted">Automatic compression and resizing</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-preview mb-3">
                                    <i class="fas fa-tags text-warning mb-2"></i>
                                    <h6>Smart Tagging</h6>
                                    <small class="text-muted">Organize with categories and tags</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-preview mb-3">
                                    <i class="fas fa-eye text-info mb-2"></i>
                                    <h6>Live Preview</h6>
                                    <small class="text-muted">See how images appear on your site</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <p class="text-muted">
                        In the meantime, you can configure your gallery display settings in the
                        <a href="{{ route('owner.landing-page.edit') }}" class="text-primary">Landing Page Editor</a>.
                    </p>
                </div>
            </div>

            <!-- Placeholder Upload Form (for future implementation) -->
            <div class="card bg-light" style="display: none;">
                <div class="card-body">
                    <form action="{{ route('owner.gallery.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="form-group">
                            <label for="images">Select Images</label>
                            <input type="file" class="form-control-file" id="images" name="images[]" multiple accept="image/*">
                            <small class="form-text text-muted">
                                You can select multiple images at once. Supported formats: JPG, PNG, GIF, WebP
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">Select a category</option>
                                <option value="portfolio">Portfolio</option>
                                <option value="before-after">Before & After</option>
                                <option value="team">Team</option>
                                <option value="facility">Facility</option>
                                <option value="products">Products</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="description">Description (Optional)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Add a description for these images..."></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="auto_optimize" name="auto_optimize" checked>
                                <label class="custom-control-label" for="auto_optimize">
                                    Automatically optimize images for web
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload mr-1"></i>
                                Upload Images
                            </button>
                            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@push('styles')
<style>
.feature-preview {
    text-align: center;
}
.feature-preview i {
    font-size: 2rem;
    display: block;
}
</style>
@endpush
