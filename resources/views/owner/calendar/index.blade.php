@extends('owner.layouts.app')

@section('title', 'Calendar')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Calendar</h1>
            <p class="text-muted">View and manage your booking schedule</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group mr-2" role="group">
                    <button type="button" class="btn btn-success" id="createBookingBtn">
                        <i class="fas fa-plus mr-1"></i>
                        New Booking
                    </button>
                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#" id="createRecurringBookingBtn">
                            <i class="fas fa-redo mr-2"></i>
                            Recurring Booking
                        </a>
                        <a class="dropdown-item" href="#" id="viewAvailabilityBtn">
                            <i class="fas fa-clock mr-2"></i>
                            View Availability
                        </a>
                    </div>
                </div>
                <div class="btn-group mr-2" role="group">
                    <button type="button" class="btn btn-warning" id="createBlockBtn">
                        <i class="fas fa-ban mr-1"></i>
                        Block Time
                    </button>
                    <button type="button" class="btn btn-warning dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#" id="manageScheduleBtn">
                            <i class="fas fa-calendar-week mr-2"></i>
                            Manage Schedule
                        </a>
                        <a class="dropdown-item" href="#" id="viewRemindersBtn">
                            <i class="fas fa-bell mr-2"></i>
                            Reminders
                        </a>
                    </div>
                </div>
                <button type="button" class="btn btn-info" id="todayScheduleBtn">
                    <i class="fas fa-calendar-day mr-1"></i>
                    Today
                </button>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Advanced Calendar Filters --}}
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h5 class="mb-0">
                        <i class="fas fa-filter mr-2"></i>
                        Calendar Filters & Search
                    </h5>
                </div>
                <div class="col-md-8 text-right">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="advancedSearchBtn">
                        <i class="fas fa-search-plus mr-1"></i> Advanced Search
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportDataBtn">
                        <i class="fas fa-download mr-1"></i> Export
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshCalendar">
                        <i class="fas fa-sync-alt mr-1"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            {{-- Quick Filters Row --}}
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label text-sm">Service</label>
                    <select class="form-control form-control-sm" id="serviceFilter">
                        <option value="">All Services</option>
                        @foreach($services as $service)
                            <option value="{{ $service->id }}">{{ $service->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label text-sm">Status</label>
                    <select class="form-control form-control-sm" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="completed">Completed</option>
                        <option value="no_show">No Show</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label text-sm">View Type</label>
                    <select class="form-control form-control-sm" id="viewFilter">
                        <option value="all">All Events</option>
                        <option value="bookings">Bookings Only</option>
                        <option value="blocks">Blocks Only</option>
                        <option value="holidays">Holidays Only</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label text-sm">Client Search</label>
                    <input type="text" class="form-control form-control-sm" id="clientSearch" placeholder="Search by name, email, phone...">
                </div>
            </div>

            {{-- Advanced Search Panel (Hidden by default) --}}
            <div id="advancedSearchPanel" class="border-top pt-3" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label text-sm">Date Range</label>
                        <div class="input-group input-group-sm">
                            <input type="date" class="form-control" id="dateFrom" placeholder="From">
                            <input type="date" class="form-control" id="dateTo" placeholder="To">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label text-sm">Time Range</label>
                        <div class="input-group input-group-sm">
                            <input type="time" class="form-control" id="timeFrom" placeholder="From">
                            <input type="time" class="form-control" id="timeTo" placeholder="To">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label text-sm">Payment Status</label>
                        <select class="form-control form-control-sm" id="paymentStatusFilter">
                            <option value="">All Payment Status</option>
                            <option value="pending">Pending</option>
                            <option value="paid">Paid</option>
                            <option value="partial">Partial</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label text-sm">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" class="form-control" id="amountFrom" placeholder="Min" step="0.01">
                            <input type="number" class="form-control" id="amountTo" placeholder="Max" step="0.01">
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-sm btn-primary" id="applyAdvancedSearch">
                            <i class="fas fa-search mr-1"></i> Apply Filters
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearAdvancedSearch">
                            <i class="fas fa-times mr-1"></i> Clear All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Calendar --}}
    <div class="card">
        <div class="card-body">
            <div id="calendar"></div>
        </div>
    </div>

    {{-- Legend --}}
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h6>Legend:</h6>
                    <span class="badge badge-warning mr-2">Pending</span>
                    <span class="badge badge-success mr-2">Confirmed</span>
                    <span class="badge badge-danger mr-2">Cancelled</span>
                    <span class="badge badge-primary mr-2">Completed</span>
                    <span class="badge badge-secondary mr-2">No Show</span>
                    <span class="badge" style="background-color: #fd7e14; color: white;" class="mr-2">Maintenance</span>
                    <span class="badge" style="background-color: #20c997; color: white;" class="mr-2">Holiday</span>
                    <span class="badge" style="background-color: #6f42c1; color: white;" class="mr-2">Private Event</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Booking Details Modal --}}
    <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Booking Details</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="bookingDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-warning" id="editBookingBtn">Edit</button>
                    <button type="button" class="btn btn-danger" id="cancelBookingBtn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Block Details Modal --}}
    <div class="modal fade" id="blockDetailsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Block Details</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="blockDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-warning" id="editBlockBtn">Edit</button>
                    <button type="button" class="btn btn-danger" id="deleteBlockBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Booking Modal --}}
    <div class="modal fade" id="quickBookingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Booking</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="quickBookingForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_service_id">Service *</label>
                                    <select class="form-control" id="booking_service_id" name="service_id" required>
                                        <option value="">Select Service</option>
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}" data-duration="{{ $service->total_duration }}" data-price="{{ $service->base_price }}">
                                                {{ $service->name }} ({{ $service->formatted_duration }} - ${{ $service->formatted_price }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_start_datetime">Date & Time *</label>
                                    <input type="datetime-local" class="form-control" id="booking_start_datetime" name="start_datetime" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_customer_name">Customer Name *</label>
                                    <input type="text" class="form-control" id="booking_customer_name" name="customer_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_customer_email">Customer Email *</label>
                                    <input type="email" class="form-control" id="booking_customer_email" name="customer_email" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_customer_phone">Customer Phone</label>
                                    <input type="tel" class="form-control" id="booking_customer_phone" name="customer_phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_participant_count">Participants *</label>
                                    <input type="number" class="form-control" id="booking_participant_count" name="participant_count" value="1" min="1" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="booking_notes">Notes</label>
                            <textarea class="form-control" id="booking_notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Create Booking</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Block Time Modal --}}
    <div class="modal fade" id="blockTimeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Block Time</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="blockTimeForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="block_title">Title *</label>
                            <input type="text" class="form-control" id="block_title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="block_type">Type *</label>
                            <select class="form-control" id="block_type" name="block_type" required>
                                <option value="">Select Type</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="holiday">Holiday</option>
                                <option value="private_event">Private Event</option>
                                <option value="staff_break">Staff Break</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_start_datetime">Start Date & Time *</label>
                                    <input type="datetime-local" class="form-control" id="block_start_datetime" name="start_datetime" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_end_datetime">End Date & Time *</label>
                                    <input type="datetime-local" class="form-control" id="block_end_datetime" name="end_datetime" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="block_description">Description</label>
                            <textarea class="form-control" id="block_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="block_affects_all_resources" name="affects_all_resources" value="1">
                            <label class="form-check-label" for="block_affects_all_resources">
                                Affects all resources
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Block Time</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Recurring Booking Modal --}}
    <div class="modal fade" id="recurringBookingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-redo mr-2"></i>
                        Create Recurring Booking
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="recurringBookingForm">
                    <div class="modal-body">
                        <div class="row">
                            {{-- Basic Booking Information --}}
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Booking Information
                                </h6>
                                <div class="form-group">
                                    <label for="recurring_service_id">Service *</label>
                                    <select class="form-control" id="recurring_service_id" name="service_id" required>
                                        <option value="">Select Service</option>
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}" data-duration="{{ $service->total_duration }}" data-price="{{ $service->base_price }}">
                                                {{ $service->name }} ({{ $service->formatted_duration }} - ${{ $service->formatted_price }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="recurring_start_datetime">First Appointment Date & Time *</label>
                                    <input type="datetime-local" class="form-control" id="recurring_start_datetime" name="start_datetime" required>
                                </div>
                                <div class="form-group">
                                    <label for="recurring_customer_name">Customer Name *</label>
                                    <input type="text" class="form-control" id="recurring_customer_name" name="customer_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="recurring_customer_email">Customer Email *</label>
                                    <input type="email" class="form-control" id="recurring_customer_email" name="customer_email" required>
                                </div>
                                <div class="form-group">
                                    <label for="recurring_customer_phone">Customer Phone</label>
                                    <input type="tel" class="form-control" id="recurring_customer_phone" name="customer_phone">
                                </div>
                                <div class="form-group">
                                    <label for="recurring_participant_count">Participants *</label>
                                    <input type="number" class="form-control" id="recurring_participant_count" name="participant_count" value="1" min="1" required>
                                </div>
                            </div>
                            {{-- Recurrence Pattern --}}
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-repeat mr-1"></i>
                                    Recurrence Pattern
                                </h6>
                                <div class="form-group">
                                    <label for="recurrence_pattern">Repeat *</label>
                                    <select class="form-control" id="recurrence_pattern" name="recurrence_pattern" required>
                                        <option value="">Select Pattern</option>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="yearly">Yearly</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="recurrence_interval">Every *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="recurrence_interval" name="recurrence_interval" value="1" min="1" required>
                                        <div class="input-group-append">
                                            <span class="input-group-text" id="intervalLabel">week(s)</span>
                                        </div>
                                    </div>
                                </div>
                                <div id="weeklyOptions" class="form-group" style="display: none;">
                                    <label>Days of the week</label>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="sun" name="weekly_days[]" value="0">
                                        <label class="form-check-label" for="sun">Sun</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="mon" name="weekly_days[]" value="1">
                                        <label class="form-check-label" for="mon">Mon</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="tue" name="weekly_days[]" value="2">
                                        <label class="form-check-label" for="tue">Tue</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="wed" name="weekly_days[]" value="3">
                                        <label class="form-check-label" for="wed">Wed</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="thu" name="weekly_days[]" value="4">
                                        <label class="form-check-label" for="thu">Thu</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="fri" name="weekly_days[]" value="5">
                                        <label class="form-check-label" for="fri">Fri</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input type="checkbox" class="form-check-input" id="sat" name="weekly_days[]" value="6">
                                        <label class="form-check-label" for="sat">Sat</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="recurrence_end_type">End *</label>
                                    <select class="form-control" id="recurrence_end_type" name="recurrence_end_type" required>
                                        <option value="never">Never</option>
                                        <option value="after_occurrences">After number of occurrences</option>
                                        <option value="on_date">On specific date</option>
                                    </select>
                                </div>
                                <div id="occurrencesGroup" class="form-group" style="display: none;">
                                    <label for="max_occurrences">Number of occurrences</label>
                                    <input type="number" class="form-control" id="max_occurrences" name="max_occurrences" min="1" max="100">
                                </div>
                                <div id="endDateGroup" class="form-group" style="display: none;">
                                    <label for="recurrence_end_date">End date</label>
                                    <input type="date" class="form-control" id="recurrence_end_date" name="recurrence_end_date">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="recurring_notes">Notes</label>
                            <textarea class="form-control" id="recurring_notes" name="notes" rows="3"></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>Preview:</strong> <span id="recurrencePreview">Select a pattern to see preview</span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-redo mr-1"></i>
                            Create Recurring Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Availability Slots Modal --}}
    <div class="modal fade" id="availabilityModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-clock mr-2"></i>
                        Available Time Slots
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="availability_service">Service</label>
                            <select class="form-control" id="availability_service">
                                <option value="">Select Service</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}">{{ $service->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="availability_date">Date</label>
                            <input type="date" class="form-control" id="availability_date" value="{{ date('Y-m-d') }}">
                        </div>
                    </div>
                    <div id="availabilitySlots">
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-3x mb-3"></i>
                            <p>Select a service and date to view available time slots</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Export Data Modal --}}
    <div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-download mr-2"></i>
                        Export Calendar Data
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="exportForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="export_format">Export Format</label>
                            <select class="form-control" id="export_format" name="format" required>
                                <option value="csv">CSV (Excel Compatible)</option>
                                <option value="pdf">PDF Report</option>
                                <option value="ical">iCal (Calendar Import)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="export_date_range">Date Range</label>
                            <select class="form-control" id="export_date_range" name="date_range">
                                <option value="current_view">Current Calendar View</option>
                                <option value="this_week">This Week</option>
                                <option value="this_month">This Month</option>
                                <option value="next_month">Next Month</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        <div id="customDateRange" class="form-group" style="display: none;">
                            <label>Custom Date Range</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="date" class="form-control" id="export_start_date" name="start_date">
                                </div>
                                <div class="col-md-6">
                                    <input type="date" class="form-control" id="export_end_date" name="end_date">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Include Data</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="include_bookings" name="include[]" value="bookings" checked>
                                <label class="form-check-label" for="include_bookings">Bookings</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="include_blocks" name="include[]" value="blocks">
                                <label class="form-check-label" for="include_blocks">Availability Blocks</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="include_customer_details" name="include[]" value="customer_details" checked>
                                <label class="form-check-label" for="include_customer_details">Customer Details</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="include_payment_info" name="include[]" value="payment_info">
                                <label class="form-check-label" for="include_payment_info">Payment Information</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download mr-1"></i>
                            Export Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_css')
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css" rel="stylesheet">
@stop

@section('adminlte_js')
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize FullCalendar
            const calendarEl = document.getElementById('calendar');
            const serviceFilter = document.getElementById('serviceFilter');
            const statusFilter = document.getElementById('statusFilter');
            const viewFilter = document.getElementById('viewFilter');

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                },
                events: function(info, successCallback, failureCallback) {
                    const params = new URLSearchParams({
                        start: info.startStr,
                        end: info.endStr,
                        view: viewFilter.value || 'all'
                    });

                    // Basic filters
                    if (serviceFilter.value) {
                        params.append('service_id', serviceFilter.value);
                    }
                    if (statusFilter.value) {
                        params.append('status', statusFilter.value);
                    }

                    // Client search
                    const clientSearch = document.getElementById('clientSearch');
                    if (clientSearch && clientSearch.value) {
                        params.append('client_search', clientSearch.value);
                    }

                    // Advanced search parameters
                    const dateFrom = document.getElementById('dateFrom');
                    const dateTo = document.getElementById('dateTo');
                    const timeFrom = document.getElementById('timeFrom');
                    const timeTo = document.getElementById('timeTo');
                    const paymentStatusFilter = document.getElementById('paymentStatusFilter');
                    const amountFrom = document.getElementById('amountFrom');
                    const amountTo = document.getElementById('amountTo');

                    if (dateFrom && dateFrom.value) {
                        params.append('date_from', dateFrom.value);
                    }
                    if (dateTo && dateTo.value) {
                        params.append('date_to', dateTo.value);
                    }
                    if (timeFrom && timeFrom.value) {
                        params.append('time_from', timeFrom.value);
                    }
                    if (timeTo && timeTo.value) {
                        params.append('time_to', timeTo.value);
                    }
                    if (paymentStatusFilter && paymentStatusFilter.value) {
                        params.append('payment_status', paymentStatusFilter.value);
                    }
                    if (amountFrom && amountFrom.value) {
                        params.append('amount_from', amountFrom.value);
                    }
                    if (amountTo && amountTo.value) {
                        params.append('amount_to', amountTo.value);
                    }

                    fetch(`{{ route('owner.calendar.events') }}?${params}`)
                        .then(response => response.json())
                        .then(data => successCallback(data))
                        .catch(error => {
                            console.error('Error loading calendar events:', error);
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    const eventType = info.event.extendedProps.type;

                    if (eventType === 'booking') {
                        showBookingDetails(info.event.extendedProps.booking_id);
                    } else if (eventType === 'block') {
                        showBlockDetails(info.event.extendedProps.block_id);
                    }
                },
                selectable: true,
                select: function(info) {
                    showQuickBookingForm(info.startStr);
                },
                editable: true,
                eventDrop: function(info) {
                    if (info.event.extendedProps.type === 'booking') {
                        updateBookingTime(info.event.extendedProps.booking_id, info.event.start, info.event.end);
                    }
                },
                eventResize: function(info) {
                    if (info.event.extendedProps.type === 'booking') {
                        updateBookingTime(info.event.extendedProps.booking_id, info.event.start, info.event.end);
                    }
                },
                height: 'auto',
                dayMaxEvents: 3,
                moreLinkClick: 'popover'
            });

            calendar.render();

            // Filter change handlers
            $('#serviceFilter, #statusFilter, #viewFilter, #clientSearch').on('change keyup', function() {
                calendar.refetchEvents();
            });

            // Advanced search toggle
            $('#advancedSearchBtn').on('click', function() {
                $('#advancedSearchPanel').slideToggle();
                const icon = $(this).find('i');
                icon.toggleClass('fa-search-plus fa-search-minus');
            });

            // Advanced search handlers
            $('#applyAdvancedSearch').on('click', function() {
                calendar.refetchEvents();
            });

            $('#clearAdvancedSearch').on('click', function() {
                $('#advancedSearchPanel input, #advancedSearchPanel select').val('');
                calendar.refetchEvents();
            });

            // Refresh button
            $('#refreshCalendar').on('click', function() {
                calendar.refetchEvents();
                showToast('success', 'Calendar refreshed successfully');
            });

            // Create booking button
            $('#createBookingBtn').on('click', function() {
                showQuickBookingForm();
            });

            // Create recurring booking button
            $('#createRecurringBookingBtn').on('click', function(e) {
                e.preventDefault();
                $('#recurringBookingModal').modal('show');
            });

            // View availability button
            $('#viewAvailabilityBtn').on('click', function(e) {
                e.preventDefault();
                $('#availabilityModal').modal('show');
            });

            // Create block button
            $('#createBlockBtn').on('click', function() {
                showBlockForm();
            });

            // Manage schedule button
            $('#manageScheduleBtn').on('click', function(e) {
                e.preventDefault();
                window.location.href = '{{ route("owner.business.operating-hours") }}';
            });

            // View reminders button
            $('#viewRemindersBtn').on('click', function(e) {
                e.preventDefault();
                // TODO: Implement reminders view
                showToast('info', 'Reminders feature coming soon');
            });

            // Today schedule button
            $('#todayScheduleBtn').on('click', function() {
                calendar.gotoDate(new Date());
                calendar.changeView('timeGridDay');
            });

            // Export data button
            $('#exportDataBtn').on('click', function() {
                $('#exportModal').modal('show');
            });

            // Show booking details modal
            function showBookingDetails(bookingId) {
                fetch(`/owner/calendar/booking/${bookingId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            $('#bookingDetailsContent').html(data.html);
                            $('#bookingDetailsModal').modal('show');
                        } else {
                            showToast('error', 'Error loading booking details');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('error', 'Error loading booking details');
                    });
            }

            // Show block details modal
            function showBlockDetails(blockId) {
                fetch(`/owner/calendar/block/${blockId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            $('#blockDetailsContent').html(data.html);
                            $('#blockDetailsModal').modal('show');
                        } else {
                            showToast('error', 'Error loading block details');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('error', 'Error loading block details');
                    });
            }

            // Show quick booking form
            function showQuickBookingForm(startDate = null) {
                if (startDate) {
                    const date = new Date(startDate);
                    const formattedDate = date.toISOString().slice(0, 16);
                    $('#booking_start_datetime').val(formattedDate);
                }
                $('#quickBookingModal').modal('show');
            }

            // Show block form
            function showBlockForm(startDate = null) {
                if (startDate) {
                    const date = new Date(startDate);
                    const formattedDate = date.toISOString().slice(0, 16);
                    $('#block_start_datetime').val(formattedDate);

                    // Set end date to 1 hour later
                    const endDate = new Date(date.getTime() + 60 * 60 * 1000);
                    const formattedEndDate = endDate.toISOString().slice(0, 16);
                    $('#block_end_datetime').val(formattedEndDate);
                }
                $('#blockTimeModal').modal('show');
            }



            // Helper functions for badge classes
            function getStatusBadgeClass(status) {
                const classes = {
                    'pending': 'warning',
                    'confirmed': 'success',
                    'cancelled': 'danger',
                    'completed': 'primary',
                    'no_show': 'secondary'
                };
                return classes[status] || 'secondary';
            }

            function getPaymentBadgeClass(status) {
                const classes = {
                    'pending': 'warning',
                    'paid': 'success',
                    'partial': 'info',
                    'refunded': 'secondary'
                };
                return classes[status] || 'secondary';
            }

            function getBlockTypeColor(type) {
                const colors = {
                    'maintenance': '#fd7e14',
                    'holiday': '#20c997',
                    'private_event': '#6f42c1',
                    'staff_break': '#6c757d',
                    'other': '#343a40'
                };
                return colors[type] || '#6c757d';
            }

            // Update booking time (drag & drop)
            function updateBookingTime(bookingId, start, end) {
                fetch(`/owner/calendar/booking/${bookingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        start_datetime: start.toISOString(),
                        end_datetime: end.toISOString()
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        showAlert('success', data.message);
                    } else {
                        // Revert the event and show error
                        calendar.refetchEvents();
                        showAlert('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    calendar.refetchEvents();
                    showAlert('error', 'Error updating booking');
                });
            }

            // Enhanced toast notification system
            function showToast(type, message, title = null) {
                const toastId = 'toast-' + Date.now();
                const iconClass = {
                    'success': 'fas fa-check-circle',
                    'error': 'fas fa-exclamation-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'info': 'fas fa-info-circle'
                }[type] || 'fas fa-info-circle';

                const bgClass = {
                    'success': 'bg-success',
                    'error': 'bg-danger',
                    'warning': 'bg-warning',
                    'info': 'bg-info'
                }[type] || 'bg-info';

                const toastHtml = `
                    <div id="${toastId}" class="toast" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                        <div class="toast-header ${bgClass} text-white">
                            <i class="${iconClass} mr-2"></i>
                            <strong class="mr-auto">${title || type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                            <button type="button" class="ml-2 mb-1 close text-white" data-dismiss="toast">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                `;

                $('body').append(toastHtml);
                $(`#${toastId}`).toast({delay: 5000}).toast('show');

                // Remove toast after it's hidden
                $(`#${toastId}`).on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            // Legacy support
            function showAlert(type, message) {
                showToast(type, message);
            }

            // Form submission handlers
            $('#quickBookingForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData);

                fetch('{{ route('owner.calendar.create-booking') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        $('#quickBookingModal').modal('hide');
                        calendar.refetchEvents();
                        showAlert('success', data.message);
                        this.reset();
                    } else {
                        showAlert('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', 'Error creating booking');
                });
            });

            $('#blockTimeForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData);

                fetch('{{ route('owner.calendar.create-block') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        $('#blockTimeModal').modal('hide');
                        calendar.refetchEvents();
                        showAlert('success', data.message);
                        this.reset();
                    } else {
                        showAlert('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', 'Error creating block');
                });
            });

            // Service selection handler for booking form
            $('#booking_service_id').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const duration = selectedOption.data('duration');

                if (duration && $('#booking_start_datetime').val()) {
                    const startDate = new Date($('#booking_start_datetime').val());
                    const endDate = new Date(startDate.getTime() + duration * 60 * 1000);
                    // You could show the end time somewhere in the form if needed
                }
            });

            // Recurring booking form handlers
            $('#recurrence_pattern').on('change', function() {
                const pattern = $(this).val();
                const intervalLabel = $('#intervalLabel');
                const weeklyOptions = $('#weeklyOptions');

                // Update interval label
                switch(pattern) {
                    case 'daily':
                        intervalLabel.text('day(s)');
                        weeklyOptions.hide();
                        break;
                    case 'weekly':
                        intervalLabel.text('week(s)');
                        weeklyOptions.show();
                        break;
                    case 'monthly':
                        intervalLabel.text('month(s)');
                        weeklyOptions.hide();
                        break;
                    case 'yearly':
                        intervalLabel.text('year(s)');
                        weeklyOptions.hide();
                        break;
                    default:
                        intervalLabel.text('week(s)');
                        weeklyOptions.hide();
                }

                updateRecurrencePreview();
            });

            $('#recurrence_end_type').on('change', function() {
                const endType = $(this).val();
                $('#occurrencesGroup').toggle(endType === 'after_occurrences');
                $('#endDateGroup').toggle(endType === 'on_date');
                updateRecurrencePreview();
            });

            $('#recurrence_interval, #max_occurrences, #recurrence_end_date').on('change', updateRecurrencePreview);

            function updateRecurrencePreview() {
                const pattern = $('#recurrence_pattern').val();
                const interval = $('#recurrence_interval').val();
                const endType = $('#recurrence_end_type').val();
                const maxOccurrences = $('#max_occurrences').val();
                const endDate = $('#recurrence_end_date').val();

                if (!pattern) {
                    $('#recurrencePreview').text('Select a pattern to see preview');
                    return;
                }

                let preview = `Every ${interval} ${pattern}`;
                if (pattern === 'weekly') {
                    const selectedDays = $('input[name="weekly_days[]"]:checked').map(function() {
                        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                        return days[$(this).val()];
                    }).get();
                    if (selectedDays.length > 0) {
                        preview += ` on ${selectedDays.join(', ')}`;
                    }
                }

                if (endType === 'after_occurrences' && maxOccurrences) {
                    preview += `, for ${maxOccurrences} occurrences`;
                } else if (endType === 'on_date' && endDate) {
                    preview += `, until ${endDate}`;
                } else if (endType === 'never') {
                    preview += `, indefinitely`;
                }

                $('#recurrencePreview').text(preview);
            }

            // Recurring booking form submission
            $('#recurringBookingForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData);

                // Add recurrence data
                data.recurrence_data = {
                    pattern: data.recurrence_pattern,
                    interval: parseInt(data.recurrence_interval),
                    end_type: data.recurrence_end_type,
                    max_occurrences: data.max_occurrences ? parseInt(data.max_occurrences) : null,
                    end_date: data.recurrence_end_date || null,
                    weekly_days: $('input[name="weekly_days[]"]:checked').map(function() {
                        return parseInt($(this).val());
                    }).get()
                };

                fetch('{{ route('owner.calendar.create-recurring-booking') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        $('#recurringBookingModal').modal('hide');
                        calendar.refetchEvents();
                        showToast('success', data.message);
                        this.reset();
                        $('#recurrencePreview').text('Select a pattern to see preview');
                    } else {
                        showToast('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('error', 'Error creating recurring booking');
                });
            });

            // Availability slots functionality
            $('#availability_service, #availability_date').on('change', function() {
                loadAvailabilitySlots();
            });

            function loadAvailabilitySlots() {
                const serviceId = $('#availability_service').val();
                const date = $('#availability_date').val();

                if (!serviceId || !date) {
                    $('#availabilitySlots').html(`
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-3x mb-3"></i>
                            <p>Select a service and date to view available time slots</p>
                        </div>
                    `);
                    return;
                }

                $('#availabilitySlots').html(`
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>Loading available slots...</p>
                    </div>
                `);

                fetch(`{{ route('owner.calendar.available-slots') }}?service_id=${serviceId}&date=${date}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayAvailabilitySlots(data.slots);
                        } else {
                            $('#availabilitySlots').html(`
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    ${data.message || 'No slots available for this date'}
                                </div>
                            `);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        $('#availabilitySlots').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle mr-2"></i>
                                Error loading availability slots
                            </div>
                        `);
                    });
            }

            function displayAvailabilitySlots(slots) {
                if (slots.length === 0) {
                    $('#availabilitySlots').html(`
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            No available slots for this date
                        </div>
                    `);
                    return;
                }

                let slotsHtml = '<div class="row">';
                slots.forEach(slot => {
                    const slotClass = slot.available ? 'btn-outline-success' : 'btn-outline-secondary';
                    const disabled = slot.available ? '' : 'disabled';
                    slotsHtml += `
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn ${slotClass} btn-block ${disabled}"
                                    onclick="selectTimeSlot('${slot.datetime}')" ${disabled}>
                                ${slot.time}
                            </button>
                        </div>
                    `;
                });
                slotsHtml += '</div>';

                $('#availabilitySlots').html(slotsHtml);
            }

            // Export functionality
            $('#export_date_range').on('change', function() {
                $('#customDateRange').toggle($(this).val() === 'custom');
            });

            $('#exportForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const params = new URLSearchParams();

                // Add form data to params
                for (let [key, value] of formData.entries()) {
                    params.append(key, value);
                }

                // Add current calendar view dates if needed
                if ($('#export_date_range').val() === 'current_view') {
                    const view = calendar.view;
                    params.append('start_date', view.activeStart.toISOString().split('T')[0]);
                    params.append('end_date', view.activeEnd.toISOString().split('T')[0]);
                }

                // Create download link
                const exportUrl = `{{ route('owner.calendar.export') }}?${params.toString()}`;

                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Exporting...').prop('disabled', true);

                // Create temporary link and trigger download
                const link = document.createElement('a');
                link.href = exportUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Reset button state
                setTimeout(() => {
                    submitBtn.html(originalText).prop('disabled', false);
                    $('#exportModal').modal('hide');
                    showToast('success', 'Export started successfully');
                }, 1000);
            });

            // Helper function for time slot selection
            window.selectTimeSlot = function(datetime) {
                $('#booking_start_datetime').val(datetime.slice(0, 16));
                $('#availabilityModal').modal('hide');
                $('#quickBookingModal').modal('show');
            };
        });
    </script>
@stop
