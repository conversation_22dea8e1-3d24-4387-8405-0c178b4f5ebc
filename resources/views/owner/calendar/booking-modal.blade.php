<div class="booking-details">
    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-calendar-check mr-2"></i>
                Booking Details
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Booking #:</strong></td>
                    <td>{{ $booking->booking_number }}</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->status_color }}">
                            {{ ucfirst($booking->status) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Date & Time:</strong></td>
                    <td>
                        {{ $booking->start_datetime->format('M d, Y') }}<br>
                        {{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                    </td>
                </tr>
                <tr>
                    <td><strong>Duration:</strong></td>
                    <td>{{ $booking->formatted_duration }}</td>
                </tr>
                <tr>
                    <td><strong>Participants:</strong></td>
                    <td>{{ $booking->participant_count }}</td>
                </tr>
                @if($booking->is_recurring)
                <tr>
                    <td><strong>Recurring:</strong></td>
                    <td>
                        <span class="badge badge-info">
                            <i class="fas fa-redo mr-1"></i>
                            Recurring Booking
                        </span>
                    </td>
                </tr>
                @endif
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-user mr-2"></i>
                Customer Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>{{ $booking->customer_name }}</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>
                        <a href="mailto:{{ $booking->customer_email }}">
                            {{ $booking->customer_email }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td>
                        @if($booking->customer_phone)
                            <a href="tel:{{ $booking->customer_phone }}">
                                {{ $booking->customer_phone }}
                            </a>
                        @else
                            <span class="text-muted">Not provided</span>
                        @endif
                    </td>
                </tr>
                @if($booking->customer)
                <tr>
                    <td><strong>Customer Since:</strong></td>
                    <td>{{ $booking->customer->created_at->format('M Y') }}</td>
                </tr>
                <tr>
                    <td><strong>Total Bookings:</strong></td>
                    <td>{{ $booking->customer->bookings()->count() }}</td>
                </tr>
                @endif
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-concierge-bell mr-2"></i>
                Services
            </h5>
            @if($booking->bookingServices->count() > 0)
                <div class="list-group list-group-flush">
                    @foreach($booking->bookingServices as $bookingService)
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $bookingService->service->name }}</h6>
                                    <small class="text-muted">
                                        Duration: {{ $bookingService->formatted_duration }}
                                    </small>
                                </div>
                                <div class="text-right">
                                    <strong>${{ number_format($bookingService->total_price, 2) }}</strong>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-muted">No services assigned</p>
            @endif
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-dollar-sign mr-2"></i>
                Payment Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Subtotal:</strong></td>
                    <td>${{ number_format($booking->subtotal, 2) }}</td>
                </tr>
                @if($booking->tax_amount > 0)
                <tr>
                    <td><strong>Tax:</strong></td>
                    <td>${{ number_format($booking->tax_amount, 2) }}</td>
                </tr>
                @endif
                @if($booking->discount_amount > 0)
                <tr>
                    <td><strong>Discount:</strong></td>
                    <td>-${{ number_format($booking->discount_amount, 2) }}</td>
                </tr>
                @endif
                <tr>
                    <td><strong>Total Amount:</strong></td>
                    <td><strong>${{ number_format($booking->total_amount, 2) }}</strong></td>
                </tr>
                <tr>
                    <td><strong>Paid Amount:</strong></td>
                    <td>${{ number_format($booking->paid_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Remaining:</strong></td>
                    <td>
                        @php $remaining = $booking->total_amount - $booking->paid_amount; @endphp
                        <span class="@if($remaining > 0) text-warning @else text-success @endif">
                            ${{ number_format($remaining, 2) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Payment Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->payment_status_color }}">
                            {{ ucfirst($booking->payment_status) }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    @if($booking->notes)
    <div class="row mt-3">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-sticky-note mr-2"></i>
                Notes
            </h5>
            <div class="alert alert-light">
                {{ $booking->notes }}
            </div>
        </div>
    </div>
    @endif

    @if($booking->internal_notes)
    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-eye-slash mr-2"></i>
                Internal Notes
            </h5>
            <div class="alert alert-warning">
                <small><i class="fas fa-lock mr-1"></i> Internal only</small><br>
                {{ $booking->internal_notes }}
            </div>
        </div>
    </div>
    @endif

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="btn-group" role="group">
                <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Full Details
                </a>
                @if($booking->can_be_checked_in)
                    <form action="{{ route('owner.bookings.check-in', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Check In
                        </button>
                    </form>
                @endif
                @if($booking->can_be_checked_out)
                    <form action="{{ route('owner.bookings.check-out', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Check Out
                        </button>
                    </form>
                @endif
                @if($booking->can_be_edited)
                    <a href="{{ route('owner.bookings.edit', $booking) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                @endif
                @if($booking->can_be_cancelled)
                    <form action="{{ route('owner.bookings.cancel', $booking) }}" method="POST" class="d-inline" 
                          onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    @if($booking->is_recurring && $booking->recurring_group_id)
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h6>
                    <i class="fas fa-redo mr-2"></i>
                    Recurring Booking Series
                </h6>
                <p class="mb-2">This booking is part of a recurring series.</p>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-info" onclick="viewRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-list mr-1"></i>
                        View All in Series
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="editRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-edit mr-1"></i>
                        Edit Series
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="cancelRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-times mr-1"></i>
                        Cancel Series
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function viewRecurringSeries(groupId) {
    // TODO: Implement view recurring series
    alert('View recurring series feature coming soon');
}

function editRecurringSeries(groupId) {
    // TODO: Implement edit recurring series
    alert('Edit recurring series feature coming soon');
}

function cancelRecurringSeries(groupId) {
    if (confirm('Are you sure you want to cancel the entire recurring series?')) {
        // TODO: Implement cancel recurring series
        alert('Cancel recurring series feature coming soon');
    }
}
</script>
